import { Image } from 'antd';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { useContext, useRef, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import ImportIcon from '../../../assets/images/inward.png';
import noPartImage from '../../../assets/images/no_partImage.jpg';
import {
  getLocalDateTime,
  mobileWidth,
} from '../../../helperFunction';
import useDebounceValue from '../../../hooks/useDebounceValue';
import {
  useAddManyProductsWithVariantsMutation,
  useDeleteManyProductsMutation,
  useGetProductFilterOptionsQuery,
  usePaginateProductsQuery,
} from '../../../slices/productApiSlice';
import { Store } from '../../../store/Store';
import { PAGINATION_LIMIT } from '../../../utils/Constant';
import CustomToolTip from '../../global/CustomToolTip';
import TruncateString from '../../global/TruncateString';
import Button from '../../global/components/Button';
import { FilterIcon, FilterV2 } from '../../global/components/FilterV2';
import Pagination from '../../global/components/Pagination';
import Spinner from '../../global/components/Spinner';
import Table from '../../global/components/Table';
import Tooltip from '../../global/components/ToolTip';
import { convertToJson } from '../../importmaster/utils';
import ProductSideBar from './ProductSideBar';


const ProductsTable = () => {
  const [checkedRows, setCheckedRows] = useState([]);
  const navigate = useNavigate();

  const importRef = useRef();

  const isMobile = useMediaQuery({ query: mobileWidth });
  const [sideBarData, setSideBarData] = useState({});
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState([]);
  const { data: filterOptions } = useGetProductFilterOptionsQuery();

  const filterConfig = [
    {
      label: 'Date',
      path: 'createdAt',
      type: 'date',
      key: 'createdAt',
    },
    {
      label: 'Id',
      path: 'id',
      type: 'multiSelect',
      key: 'id',
      options: filterOptions?.id || [],
    },
    {
      label: 'Name',
      path: 'name',
      type: 'multiSelect',
      key: 'name',
      options: filterOptions?.name || [],
    },
    {
      label: 'UOM',
      path: 'uom',
      type: 'multiSelect',
      key: 'uom',
      options: filterOptions?.uom || [],
    },
  ];

  const [deleteProducts] = useDeleteManyProductsMutation();
  const [searchTerm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [selectAll, setSelectAll] = useState(false);

  const [addManyProductsWithVariants, { isLoading: isLoadingAddMany }] =
    useAddManyProductsWithVariantsMutation();


  const debounceSearch = useDebounceValue(searchTerm) || '';

  const { data: pageData } = usePaginateProductsQuery(
    {
      page,
      limit,
      debounceSearch,
      filters,
      type,
      field,
    },
    { refetchOnMountOrArgChange: true, skip: !page || !limit }
  );

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  function handleEdit(data) {
    navigate(`manage/${data?._id}`);
  }

  const handleDelete = async () => {
    const idsToDelete = checkedRows?.map((item) => item._id);
    const res = await deleteProducts({ ids: idsToDelete });
    if (res) {
      toast.success('Products deleted Successfully');
    }
    setCheckedRows([]);
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
  };



  const downloadTemplate = () => {
    const template = [
      {
        Product: '',
        Variant1: '',
        Variant2: '',
        Unit: '',
        Category: '',
        Rate: '',
        HSN: '',
        CGST: '',
        SGST: '',
        IGST: '',
        Discount: '',
        Store1: '',
        Store2: '',
      },
    ];

    const ws = XLSX.utils.json_to_sheet(template);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    XLSX.writeFile(wb, 'productMasters.xlsx');
  };

  return (
    <div>
      {ShowSidebar && (
        <ProductSideBar
          openSideBar={ShowSidebar}
          setOpenSideBar={setShowSidebar}
          data={sideBarData}
        />
      )}
      {isLoadingAddMany && (
        <div className="flex w-full gap-5 justify-center items-center">
          <Spinner className="!w-fit" size={6} width={4} /> Importing your
          data from {importRef?.current?.value?.split('\\')?.[2] || 'Excel'}
          , Please be patient this can take some time.
        </div>
      )}
      <div className="flex justify-between w-full bg-white rounded-tl-lg rounded-tr-lg mt-2">
        <div className="flex w-full items-center py-2 justify-between">
          <div className="flex justify-between items-center relative">
            <div className="absolute top-[-5px] left-[-340px]  md:left-[-326px]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6 absolute top-3 left-[22rem] text-gray-400 cursor-pointer"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                />
              </svg>
            </div>
            <input
              className="w-[190px] md:!min-w-[100px] !pl-10 ml-1 md:ml-3 md:w-[400px] !rounded-3xl !px-5 !py-1.5 outline-none !text-xs md:!text-sm bg-[#F2F1FB] mt-1 md:mt-0"
              placeholder="Search Items from list"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          <div className="flex gap-3 mr-4">
            {!isMobile && (
              <CustomToolTip
                tooltipId="delete-tooltip"
                content="Delete"
                place="top" // Position the tooltip above the icon
                effect="solid" // Tooltip effect
                className={`bg-black text-white p-1 rounded ${checkedRows?.length < 1 ? 'hidden' : ''}`}
              >
                <p onClick={handleDelete} className="mt-2">
                  <div className="relative group cursor-pointer">
                    <svg
                      width="21"
                      height="21"
                      viewBox="0 0 21 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className={`${checkedRows?.length < 1 ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
                    >
                      <g clipPath="url(#clip0_2579_1107)">
                        <path
                          d="M1.5 5.25H19.5"
                          stroke="#56555C"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.75 5.25H17.25V18.75C17.25 19.1478 17.092 19.5294 16.8107 19.8107C16.5294 20.092 16.1478 20.25 15.75 20.25H5.25C4.85218 20.25 4.47064 20.092 4.18934 19.8107C3.90804 19.5294 3.75 19.1478 3.75 18.75V5.25Z"
                          stroke="#56555C"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M6.75 5.25V4.5C6.75 3.50544 7.14509 2.55161 7.84835 1.84835C8.55161 1.14509 9.50544 0.75 10.5 0.75C11.4946 0.75 12.4484 1.14509 13.1517 1.84835C13.8549 2.55161 14.25 3.50544 14.25 4.5V5.25"
                          stroke="#56555C"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_2579_1107">
                          <rect width="21" height="21" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                </p>
              </CustomToolTip>
            )}

            {!isMobile && (
              <div className="mt-3">
                <FilterIcon
                  showFilters={showFilters}
                  setShowFilters={setShowFilters}
                />
              </div>
            )}
            <input
              type="file"
              className="hidden"
              ref={importRef}
              onChange={(e) => {
                const file = e.target?.files?.[0];
                const readFile = () => {
                  const reader = new FileReader();

                  reader.onload = async (evt) => {
                    const bstr = evt.target.result;
                    const wb = XLSX.read(bstr, { type: 'binary' });
                    let payload = [];
                    for (let i = 0; i < wb.SheetNames.length; i++) {
                      const wsname = wb.SheetNames[i];
                      const ws = wb.Sheets[wsname];

                      const data = XLSX.utils.sheet_to_csv(ws, {
                        header: 1,
                      });
                      payload.push(...convertToJson(data));
                    }

                    addManyProductsWithVariants({
                      data: { productsList: payload },
                    })
                      .unwrap()
                      .then((res) => {
                        if (res?.status === 200) {
                          toast.success('Data imported successfully');
                        }
                        importRef.current.value = '';
                      });
                  };
                  reader.readAsBinaryString(file);
                };

                readFile();
              }}
            />

            <Button
              disabled={isLoadingAddMany}
              className={`!bg-green-600 !text-white border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={() => importRef.current.click()}
            >
              <img
                src={ImportIcon}
                alt="Import Icon"
                className="w-4 h-4 object-contain relative"
              />
              Import
            </Button>

            <Button
              className={`!bg-blue-500 !text-white border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={downloadTemplate}
            >
              Sample File
            </Button>

            <Button
              className={`border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={() => navigate('manage')}
            >
              + Add Row
            </Button>
          </div>
        </div>
      </div>
      {!pageData?.isFetching && (
        <>
          <FilterV2
            showFilters={showFilters}
            config={filterConfig}
            setFilters={setFilters}
          />
          <section className="w-full overflow-x-scroll bg-white">
            <Table>
              <Table.Head>
                <Table.Row>
                  {!isMobile && (
                    <Table.Th>
                      {checkedRows.length > 0 ? (
                        <div>
                          <input
                            type="checkbox"
                            className="mr-2"
                            checked={selectAll}
                            onChange={(e) => setSelectAll(e.target.checked)}
                          />
                          Select All
                        </div>
                      ) : (
                        ''
                      )}
                    </Table.Th>
                  )}
                  {!isMobile && (
                    <Table.Th>
                      <div className="flex item-center">
                        <div>Date</div>
                        {type === 'aesc' && field === 'createdAt' ? (
                          <ArrowUp
                            cursor={'pointer'}
                            size={15}
                            onClick={() => {
                              setField('createdAt');
                              setType('desc');
                            }}
                          />
                        ) : (
                          <ArrowDown
                            cursor={'pointer'}
                            size={15}
                            onClick={() => {
                              setField('createdAt');
                              setType('aesc');
                            }}
                          />
                        )}
                      </div>
                    </Table.Th>
                  )}
                  {!isMobile && <Table.Th>Thumbnail</Table.Th>}
                  <Table.Th>
                    {defaultParam?.inventoryIdDefaults?.productDefaults ===
                      'Id' && 'FG Product id'}
                  </Table.Th>

                  {!isMobile
                    ? columns
                      ?.filter(
                        (item) =>
                          item.field !== 'attachments' &&
                          item.field !== 'description'
                      )
                      ?.map((e) => (
                        <Table.Th key={e?.title}>
                          <div className="flex item-center">
                            <div>{e.title}</div>
                            {e.title === 'Name' &&
                              (type === 'aesc' && field === 'name' ? (
                                <ArrowUp
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setField('name');
                                    setType('desc');
                                  }}
                                />
                              ) : (
                                <ArrowDown
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setField('name');
                                    setType('aesc');
                                  }}
                                />
                              ))}
                          </div>
                        </Table.Th>
                      ))
                    : columns
                      ?.filter((item) => item.field === 'name')
                      ?.map((e) => (
                        <Table.Th key={e?.title}>
                          <div className="flex item-center">
                            <div>{e.title}</div>
                            {e.title === 'Name' &&
                              (type === 'aesc' && field === 'name' ? (
                                <ArrowUp
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setField('name');
                                    setType('desc');
                                  }}
                                />
                              ) : (
                                <ArrowDown
                                  cursor={'pointer'}
                                  size={15}
                                  onClick={() => {
                                    setField('name');
                                    setType('aesc');
                                  }}
                                />
                              ))}
                          </div>
                        </Table.Th>
                      ))}
                  <Table.Th></Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {pageData?.results?.map((e) => {
                  return (
                    <Table.Row
                      key={e._id}
                      isClickable={true}
                      onClick={() => setSideBarData(e)}
                    >
                      {!isMobile && (
                        <Table.Td>
                          <input
                            type="checkbox"
                            // onClick={() => addTocheckedRows(e._id)}
                            onChange={(event) =>
                              setCheckedRows(event.target.checked)
                            }
                            checked={checkedRows.includes(e)}
                          />
                        </Table.Td>
                      )}

                      {!isMobile && (
                        <Table.Td
                          onClick={() => {
                            setShowSidebar(true);
                          }}
                        >
                          {getLocalDateTime(e.createdAt)}
                        </Table.Td>
                      )}
                      {!isMobile && (
                        <Table.Td>
                          {e?.thumbNail?.data ? (
                            <Image
                              className="object-contain rounded-lg cursor-pointer bg-contain"
                              src={e?.thumbNail?.data}
                              alt=""
                              width={48}
                              height={48}
                            />
                          ) : (
                            <img
                              className="w-12 h-12  object-cover bg-contain"
                              src={noPartImage}
                              alt=""
                            />
                          )}
                        </Table.Td>
                      )}

                      <Table.Td
                        onClick={() => {
                          setShowSidebar(true);
                        }}
                      >
                        <TruncateString length={13}>
                          {e.id ? e.id : '-'}
                        </TruncateString>
                      </Table.Td>
                      {!isMobile
                        ? columns
                          ?.filter(
                            (item) =>
                              item.field !== 'attachments' &&
                              item.field !== 'files' &&
                              item.field !== 'description'
                          )

                          ?.map((col) => {
                            if (col.field === 'name') {
                              return (
                                <Table.Td
                                  className={'!min-w-[24rem] !w-[24rem]'}
                                  onClick={() => {
                                    setShowSidebar(true);
                                  }}
                                  key={col.field}
                                >
                                  <div key={col._id} className="flex">
                                    <p className="cursor-pointer">
                                      {e[col?.field]?.length <= 55 ? (
                                        e[col?.field]?.length > 0 ? (
                                          e[col?.field]
                                        ) : (
                                          '-'
                                        )
                                      ) : (
                                        <Tooltip text={e[col?.field]}>
                                          {e[col?.field]?.slice(0, 55) +
                                            '...'}
                                        </Tooltip>
                                      )}
                                    </p>
                                  </div>
                                </Table.Td>
                              );
                            }
                            if (
                              col.field === 'parts' ||
                              col.field === 'subAssemblies'
                            ) {
                              return (
                                <Table.Td
                                  key={col.field}
                                  onClick={() => {
                                    setShowSidebar(true);
                                  }}
                                >
                                  <Button className="!bg-transparent !p-0 !text-blue-500 !font-medium">
                                    {col.field === 'parts'
                                      ? 'Parts'
                                      : 'Sub Assembly'}{' '}
                                    ({e[col.field]?.length})
                                  </Button>
                                </Table.Td>
                              );
                            }

                            return (
                              <Table.Td
                                key={col.field}
                                onClick={() => {
                                  setShowSidebar(true);
                                }}
                              >
                                {e[col.field] ||
                                  e?.additionalFields?.[col.field]
                                  ? e[col.field] ||
                                  e?.additionalFields?.[col.field]
                                  : '-'}
                              </Table.Td>
                            );
                          })
                        : columns
                          ?.filter((item) => item.field === 'name')
                          ?.map((col) => {
                            if (col.field === 'name') {
                              return (
                                <Table.Td
                                  className={'!min-w-[24rem] !w-[24rem]'}
                                  onClick={() => {
                                    setShowSidebar(true);
                                  }}
                                  key={col.field}
                                >
                                  <div key={col._id} className="flex">
                                    <p className="cursor-pointer">
                                      {e[col?.field]?.length <= 55 ? (
                                        e[col?.field]?.length > 0 ? (
                                          e[col?.field]
                                        ) : (
                                          '-'
                                        )
                                      ) : (
                                        <Tooltip text={e[col?.field]}>
                                          {e[col?.field]?.slice(0, 55) +
                                            '...'}
                                        </Tooltip>
                                      )}
                                    </p>
                                  </div>
                                </Table.Td>
                              );
                            }
                          })}
                      <Table.Td
                        onClick={() => {
                          setShowSidebar(true);
                        }}
                      >
                        <p className="text-blue-600 text-xs cursor-pointer">
                          Files{' '}
                          <span className="text-[8px]">
                            ({e?.attachments.length})
                          </span>
                        </p>
                      </Table.Td>
                      <Table.Options
                        className="z-10 bg-white"
                        onEdit={() => handleEdit(e)}
                      />
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </section>
          <Pagination
            limit={limit}
            page={page}
            totalPages={pageData?.totalPages}
            totalResults={pageData?.totalResults}
            setPage={setPage}
            setLimit={setLimit}
            className={`w-full`}
          />
        </>
      )}
      {pageData?.isFetching && <Spinner />}
    </div>
  );
};

export default ProductsTable;
